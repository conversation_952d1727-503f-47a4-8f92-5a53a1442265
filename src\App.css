/* Global responsive styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

/* Ensure form elements don't overflow on small screens */
.MuiTextField-root,
.MuiFormControl-root {
  max-width: 100%;
}

/* Responsive table-like layouts */
@media (max-width: 600px) {
  .responsive-table-row {
    flex-direction: column !important;
    align-items: stretch !important;
    gap: 8px !important;
  }

  .responsive-table-header {
    flex-wrap: wrap !important;
    justify-content: center !important;
  }

  .responsive-radio-group {
    justify-content: center !important;
    flex-wrap: wrap !important;
  }
}

/* Ensure images are responsive */
img {
  max-width: 100%;
  height: auto;
}

/* Prevent horizontal scrolling */
.MuiContainer-root {
  overflow-x: hidden;
}

/* Responsive dialog */
@media (max-width: 600px) {
  .MuiDialog-paper {
    margin: 16px !important;
    width: calc(100% - 32px) !important;
    max-height: calc(100% - 32px) !important;
  }
}