/* Global responsive styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

/* Ensure form elements don't overflow on small screens */
.MuiTextField-root,
.MuiFormControl-root {
  max-width: 100%;
}

/* Responsive table-like layouts */
@media (max-width: 600px) {
  .responsive-table-row {
    flex-direction: column !important;
    align-items: stretch !important;
    gap: 8px !important;
  }

  .responsive-table-header {
    flex-wrap: nowrap !important;
    justify-content: space-around !important;
    overflow: hidden !important;
  }

  .responsive-radio-group {
    justify-content: space-around !important;
    flex-wrap: nowrap !important;
    overflow: hidden !important;
  }

  /* Ensure radio button tables maintain column alignment */
  .MuiRadioGroup-row {
    justify-content: space-around !important;
    flex-wrap: nowrap !important;
  }

  /* Typography in radio tables should be evenly distributed */
  .MuiBox-root > .MuiTypography-body2 {
    flex: 1 1 0 !important;
    min-width: 50px !important;
    max-width: 70px !important;
    text-align: center !important;
  }

  /* Radio button form control labels should be evenly distributed */
  .MuiRadioGroup-row .MuiFormControlLabel-root {
    flex: 1 1 0 !important;
    min-width: 50px !important;
    max-width: 70px !important;
    margin: 0 !important;
    justify-content: center !important;
  }
}

/* Ensure images are responsive */
img {
  max-width: 100%;
  height: auto;
}

/* Prevent horizontal scrolling */
.MuiContainer-root {
  overflow-x: hidden;
}

/* Responsive dialog */
@media (max-width: 600px) {
  .MuiDialog-paper {
    margin: 16px !important;
    width: calc(100% - 32px) !important;
    max-height: calc(100% - 32px) !important;
  }
}